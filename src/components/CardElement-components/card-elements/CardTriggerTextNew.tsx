"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";

import { useRef, useEffect, useCallback } from "react";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";
import { replaceSpacesInTextContent } from "@/utils/htmlUtils";

export default function CardTriggerTextNew() {
  const printReady = useGetStoreState("printReady");
  const ability = useGetStoreState("triggerText") as string;
  const triggerTextFontSize = useGetStoreState("triggerTextFontSize") as number;
  const target = useRef<HTMLDivElement>(null);
  const fontsize = useGetResponsiveFontsizeInPx({
    useTriggerTextSizeState: true,
  });

  // Default font size threshold
  const defaultFontSize = 19.8;
  const useLegacyBehavior = triggerTextFontSize >= defaultFontSize;

  // Calculate line heights for new behavior (when font size is below default)
  const firstLineHeight = printReady
    ? `${getPrintReadySizeNumber(1.8084 * (19.8 / triggerTextFontSize), printReady)}em`
    : `${1.6484 * (19.8 / triggerTextFontSize)}em`;

  // Calculate subsequent line height with minimum constraint
  const calculateSubsequentLineHeight = () => {
    const baseMultiplier = printReady ? 1.2 : 1.1;
    const calculatedValue = baseMultiplier * (triggerTextFontSize / 19.8);
    const minValue = 0.822222;

    // If calculated value would be below minimum, use a different multiplier
    if (calculatedValue < minValue) {
      const adjustedMultiplier =
        baseMultiplier * (triggerTextFontSize / (triggerTextFontSize * 1.2));
      return printReady
        ? `${getPrintReadySizeNumber(adjustedMultiplier, printReady)}em`
        : `${adjustedMultiplier}em`;
    }

    return printReady
      ? `${getPrintReadySizeNumber(calculatedValue, printReady)}em`
      : `${calculatedValue}em`;
  };

  const subsequentLineHeight = calculateSubsequentLineHeight();

  // Calculate line height for legacy behavior (when font size is default or higher)
  const legacyLineHeight = printReady
    ? `${getPrintReadySizeNumber(1.8084 * (19.8 / triggerTextFontSize), printReady)}em`
    : `${1.6484 * (19.8 / triggerTextFontSize)}em`;

  // Function to apply line height styling (only used when font size is below default)
  const applyLineHeightStyling = useCallback(() => {
    if (target.current && !useLegacyBehavior) {
      const paragraphs = target.current.querySelectorAll("p");
      paragraphs.forEach((p, index) => {
        if (index === 0) {
          // First paragraph keeps the original line height
          p.style.lineHeight = firstLineHeight;
          p.style.marginTop = "";
          p.style.paddingBottom = "";
        } else if (index === 1) {
          // Second paragraph gets adjusted line height and margin-top for proper spacing from first element
          p.style.lineHeight = subsequentLineHeight;
          p.style.marginTop = printReady
            ? `${getPrintReadySizeNumber(0.3 * (triggerTextFontSize / 19.8), printReady)}em`
            : `${0.25 * (triggerTextFontSize / 19.8)}em`;
          p.style.paddingBottom = "";
        } else {
          // Other paragraphs get adjusted line height
          p.style.lineHeight = subsequentLineHeight;
          p.style.marginTop = "";
          p.style.paddingBottom = "";
        }

        // Last paragraph gets padding-bottom for proper distance from TriggerElement's edge
        if (index === paragraphs.length - 1) {
          p.style.paddingBottom = printReady
            ? `${getPrintReadySizeNumber(0.3 * (triggerTextFontSize / 19.8), printReady)}em`
            : `${0.3 * (triggerTextFontSize / 19.8)}em`;
        }
      });
    }
  }, [
    useLegacyBehavior,
    firstLineHeight,
    subsequentLineHeight,
    printReady,
    triggerTextFontSize,
  ]);

  // Function to clear individual paragraph styling (when switching back to legacy behavior)
  const clearIndividualStyling = useCallback(() => {
    if (target.current) {
      const paragraphs = target.current.querySelectorAll("p");
      paragraphs.forEach((p) => {
        p.style.lineHeight = "";
        p.style.marginTop = "";
        p.style.paddingBottom = "";
      });
    }
  }, []);

  useEffect(() => {
    if (useLegacyBehavior) {
      // Clear any individual paragraph styling when using legacy behavior
      clearIndividualStyling();
    } else {
      // Apply individual paragraph styling when font size is below default
      applyLineHeightStyling();
    }
  }, [
    ability, // Only trigger when triggerText content changes
    useLegacyBehavior, // Only trigger when behavior mode changes
    printReady, // Trigger when print ready state changes
    applyLineHeightStyling,
    clearIndividualStyling,
  ]);

  useEffect(() => {
    if (!useLegacyBehavior) {
      // Also apply styling after the DOM has been updated with dangerouslySetInnerHTML
      const timeoutId = setTimeout(() => {
        applyLineHeightStyling();
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [ability, applyLineHeightStyling, useLegacyBehavior]); // Only trigger when triggerText content changes

  // Effect specifically for font size and print ready changes
  useEffect(() => {
    if (!useLegacyBehavior) {
      // Use requestAnimationFrame to ensure DOM is fully rendered
      const rafId = requestAnimationFrame(() => {
        applyLineHeightStyling();
      });

      return () => cancelAnimationFrame(rafId);
    } else {
      // Clear individual styling when switching to legacy behavior
      clearIndividualStyling();
    }
  }, [
    triggerTextFontSize,
    printReady,
    applyLineHeightStyling,
    useLegacyBehavior,
    clearIndividualStyling,
  ]); // Trigger when font size or print ready changes

  // Effect for behavior mode changes
  useEffect(() => {
    if (useLegacyBehavior) {
      clearIndividualStyling();
    } else {
      // Apply styling when switching to new behavior
      const rafId = requestAnimationFrame(() => {
        applyLineHeightStyling();
      });
      return () => cancelAnimationFrame(rafId);
    }
  }, [useLegacyBehavior, applyLineHeightStyling, clearIndividualStyling]); // Only trigger when behavior mode changes

  // Observer to watch for DOM changes (only when not using legacy behavior)
  useEffect(() => {
    if (!target.current || useLegacyBehavior) return;

    const observer = new MutationObserver(() => {
      // DOM content changed, reapply styling
      applyLineHeightStyling();
    });

    observer.observe(target.current, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [useLegacyBehavior, applyLineHeightStyling]);

  return (
    <div
      ref={target}
      dangerouslySetInnerHTML={{ __html: replaceSpacesInTextContent(ability) }}
      id={"trigger-text"}
      style={{
        fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        // Apply legacy line height when font size is default or higher
        ...(useLegacyBehavior && {
          lineHeight: legacyLineHeight,
        }),
        left: "1.75%",
        width: "98%",
        zIndex: 1,
        letterSpacing: `-0.025em`,
        height: "auto", // Allow height to adjust based on content
        minHeight: "fit-content", // Ensure it fits the content
      }}
      className="trigger-text font-geologica relative text-left font-light break-words"
    ></div>
  );
}
