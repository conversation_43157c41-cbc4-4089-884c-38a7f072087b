"use client";

import { TextStyle } from "@tiptap/extension-text-style";
import { Bold } from "@tiptap/extension-bold";
import { useDispatch, useSelector } from "react-redux";
import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  onAbility,
  onAbilityDropShadow,
  onAbilityTextSize,
  onDropShadow,
  onEditorState,
  onInputField,
} from "@/store/formSlice";
import { storeState } from "@/store/store";
import { StarterKit } from "@tiptap/starter-kit";
import { Underline } from "@tiptap/extension-underline";
import Highlight from "@tiptap/extension-highlight";
import { TextAlign } from "@tiptap/extension-text-align";
import { Color } from "@tiptap/extension-color";
import { BubbleMenu, EditorContent, useEditor, Editor } from "@tiptap/react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Italic } from "@tiptap/extension-italic";
import { Button } from "@/components/ui/button";
import {
  CircleSlash2,
  ItalicIcon,
  Pipette,
  RemoveFormatting,
} from "lucide-react";

import { ColorPicker, ConfigProvider, theme } from "antd";
import type { Color as ColorAntd } from "antd/es/color-picker";
import { cn } from "@/lib/utils";
import { useDarkMode } from "@/hooks/useDarkMode";

const CustomParagraph = TextStyle.extend({
  renderHTML({ HTMLAttributes }) {
    if (HTMLAttributes.class === "orange-ability") {
      return [
        "span",
        { class: "orange-ability-container" },
        [
          "span",
          { class: "orange-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else if (HTMLAttributes.class === "black-ability") {
      return [
        "span",
        { class: "black-ability-container" },
        [
          "span",
          { class: "black-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else if (HTMLAttributes.class === "trigger-ability") {
      return [
        "span",
        { class: "trigger-ability-container" },
        [
          "span",
          { class: "trigger-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else {
      return ["span", { ...HTMLAttributes }, 0];
    }
  },
  addAttributes() {
    return {
      color: {
        // … and customize the HTML rendering.
        renderHTML: (attributes) => {
          const color = attributes.color;
          const classColor =
            color === "#2F77B3"
              ? "blue-ability"
              : color === "#d94880"
                ? "pink-ability"
                : color === "#DC8535"
                  ? "orange-ability"
                  : color === "#ba212f"
                    ? "red-ability"
                    : color === "#f8ed70"
                      ? "trigger-ability"
                      : color === "#FFFFFF"
                        ? "white-ability"
                        : color === "#000000"
                          ? "black-ability"
                          : "";
          return {
            class: classColor,
          };
        },
      },
    };
  },
});

const CustomBold = Bold.extend({
  name: "customBold",
  renderHTML({ HTMLAttributes }) {
    // Original:
    // return ['strong', HTMLAttributes, 0]
    return ["b", HTMLAttributes, 0];
  },
});
const CustomItalic = Italic.extend({
  name: "customItalic",
  renderHTML({ HTMLAttributes }) {
    const attributes = HTMLAttributes;
    attributes.class = `font-one-piece-italic-bold text-[0.94em]`;
    // Original:
    // return ['strong', HTMLAttributes, 0]
    return ["em", attributes, 0];
  },
});

// Memoized theme configuration to prevent recreation on every render
const createThemeConfig = (isDark: boolean) => ({
  components: { ColorPicker: {} },
  algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
  token: {
    colorBgElevated: isDark ? "#121212" : "#ffffff",
    colorText: isDark ? "#f9fafb" : "#000000",
    colorBorder: isDark ? "#121212" : "#d1d5db",
  },
});

// Memoized color presets to prevent recreation
const COLOR_PRESETS = [
  {
    label: "Common Colors",
    colors: [
      "#25262b",
      "#868e96",
      "#fa5252",
      "#e64980",
      "#be4bdb",
      "#7950f2",
      "#4c6ef5",
      "#228be6",
      "#15aabf",
      "#12b886",
      "#40c057",
      "#82c91e",
      "#fab005",
      "#fd7e14",
    ],
  },
];

// Check EyeDropper support once
const IS_EYEDROPPER_SUPPORTED =
  typeof window !== "undefined" && "EyeDropper" in window;

const ColorPickerButton = React.memo(
  ({ editor }: { editor: Editor | null }) => {
    const [currentColor, setCurrentColor] = useState("#000000");
    const debounceRef = useRef<NodeJS.Timeout | null>(null);
    const isDark = useDarkMode();

    // Memoize theme config to prevent recreation
    const themeConfig = useMemo(() => createThemeConfig(isDark), [isDark]);

    // Optimized color tracking - only update when color actually changes
    useEffect(() => {
      if (!editor) return;

      const updateColor = () => {
        const color = editor.getAttributes("textStyle").color || "#000000";
        setCurrentColor((prev) => (prev !== color ? color : prev));
      };

      // Update immediately
      updateColor();

      // Listen to editor updates instead of selection changes
      const handleUpdate = () => updateColor();
      editor.on("selectionUpdate", handleUpdate);
      editor.on("transaction", handleUpdate);

      return () => {
        editor.off("selectionUpdate", handleUpdate);
        editor.off("transaction", handleUpdate);
      };
    }, [editor]);

    // Cleanup debounce on unmount
    useEffect(() => {
      return () => {
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }
      };
    }, []);

    const handleColorChange = useCallback(
      (color: ColorAntd) => {
        const hexColor = color.toHexString();

        // Update UI immediately for responsive feel
        setCurrentColor(hexColor);

        // Debounce editor updates to prevent excessive re-renders
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }

        debounceRef.current = setTimeout(() => {
          if (editor && !editor.isDestroyed) {
            editor.chain().focus().setColor(hexColor).run();
          }
        }, 100);
      },
      [editor],
    );

    // EyeDropper functionality
    const handleEyeDropper = useCallback(async () => {
      if (!IS_EYEDROPPER_SUPPORTED) {
        return; // Browser doesn't support EyeDropper
      }

      try {
        // @ts-expect-error - EyeDropper is not in TypeScript types yet
        const eyeDropper = new window.EyeDropper();
        const result = await eyeDropper.open();

        if (result?.sRGBHex) {
          setCurrentColor(result.sRGBHex);

          // Apply to editor with debounce
          if (debounceRef.current) {
            clearTimeout(debounceRef.current);
          }

          debounceRef.current = setTimeout(() => {
            if (editor && !editor.isDestroyed) {
              editor.chain().focus().setColor(result.sRGBHex).run();
            }
          }, 100);
        }
      } catch (error) {
        // User cancelled or error occurred
        console.log("EyeDropper cancelled or failed:", error);
      }
    }, [editor]);

    return (
      <ConfigProvider theme={themeConfig}>
        <div className="divide-input flex items-center divide-x rounded-l-sm rounded-r-sm border-1">
          <ColorPicker
            value={currentColor}
            onChange={handleColorChange}
            size="middle"
            showText={false}
            placement="bottomLeft"
            className={
              "bg-background! hover:bg-accent! hover:text-accent-foreground! rounded-l-sm! rounded-r-none! shadow-xs! hover:border-transparent!"
            }
            arrow={false}
            presets={COLOR_PRESETS}
          />
          {IS_EYEDROPPER_SUPPORTED && (
            <Button
              variant="editor"
              size="editor"
              className="h-8 w-8 rounded-l-none rounded-r-none border-l-1 p-0"
              onClick={handleEyeDropper}
              title="Pick color from screen"
            >
              <Pipette className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="editor"
            size="editor"
            onClick={() => editor?.chain().focus().unsetAllMarks().run()}
            className={"rounded-l-none rounded-r-sm"}
          >
            <CircleSlash2 className={"w-3.5! rotate-90"} />
          </Button>
        </div>
      </ConfigProvider>
    );
  },
);

ColorPickerButton.displayName = "ColorPickerButton";

const DropShadowButton = React.memo(() => {
  const dispatch = useDispatch();
  const dropShadow = useGetStoreState("dropShadow");

  const handleClick = useCallback(() => {
    dispatch(onDropShadow());
  }, [dispatch]);

  const buttonClassName = useMemo(
    () =>
      `h-[34px] text-[14px]! px-2! ${dropShadow ? "bg-neutral-800 text-neutral-50 transition-all duration-100 dark:bg-neutral-200 dark:text-neutral-950" : ""}`,
    [dropShadow],
  );

  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          onClick={handleClick}
          className={buttonClassName}
        >
          Text Outline
        </Button>
      </TooltipTrigger>
      <TooltipContent>Toggle text outline</TooltipContent>
    </Tooltip>
  );
});

DropShadowButton.displayName = "DropShadowButton";

const AbilityDropShadowButton = React.memo(() => {
  const dispatch = useDispatch();
  const abilityDropShadow = useGetStoreState("abilityDropShadow");

  const handleClick = useCallback(() => {
    dispatch(onAbilityDropShadow());
  }, [dispatch]);

  const buttonClassName = useMemo(
    () =>
      `h-[34px] text-[14px]! px-2! ${abilityDropShadow ? "bg-neutral-800 text-neutral-50 transition-all duration-100 dark:bg-neutral-200 dark:text-neutral-950" : ""}`,
    [abilityDropShadow],
  );

  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          onClick={handleClick}
          className={buttonClassName}
        >
          Ability Outline
        </Button>
      </TooltipTrigger>
      <TooltipContent>Toggle ability outline</TooltipContent>
    </Tooltip>
  );
});

AbilityDropShadowButton.displayName = "AbilityDropShadowButton";

const Background = React.memo(() => {
  const dispatch = useDispatch();
  const abilityBackground = useGetStoreState("abilityBackground");

  const handleClick = useCallback(() => {
    dispatch(onInputField("abilityBackground"));
  }, [dispatch]);

  const buttonClassName = useMemo(
    () =>
      `h-[34px] text-[14px]! px-2! ${abilityBackground ? "bg-neutral-800 text-neutral-50 transition-all duration-100 dark:bg-neutral-200 dark:text-neutral-950" : ""}`,
    [abilityBackground],
  );

  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          onClick={handleClick}
          className={buttonClassName}
        >
          Background
        </Button>
      </TooltipTrigger>
      <TooltipContent>Toggle ability background</TooltipContent>
    </Tooltip>
  );
});

Background.displayName = "Background";

// Toolbar buttons - removed memo to allow re-renders when editor state changes
const FormattingToolbar = ({ editor }: { editor: Editor | null }) => {
  const isBoldActive = editor?.isActive("customBold") ?? false;
  const isItalicActive = editor?.isActive("customItalic") ?? false;
  const isUnderlineActive = editor?.isActive("underline") ?? false;
  const isStrikeActive = editor?.isActive("strike") ?? false;

  const handleBold = useCallback(() => {
    editor?.chain().focus().toggleBold().run();
  }, [editor]);

  const handleItalic = useCallback(() => {
    editor?.chain().focus().toggleItalic().run();
  }, [editor]);

  const handleUnderline = useCallback(() => {
    editor?.chain().focus().toggleUnderline().run();
  }, [editor]);

  const handleStrike = useCallback(() => {
    editor?.chain().focus().toggleStrike().run();
  }, [editor]);

  const handleClearMarks = useCallback(() => {
    editor?.chain().focus().unsetAllMarks().run();
  }, [editor]);

  return (
    <div
      className={
        "divide-input border-input! flex min-w-[170px]! justify-stretch divide-x rounded-sm border"
      }
    >
      <Button
        variant="editor"
        size="editor"
        onClick={handleBold}
        className={
          isBoldActive
            ? "rounded-l-sm rounded-r-none bg-neutral-800 text-neutral-50"
            : "rounded-l-sm rounded-r-none"
        }
      >
        <strong>B</strong>
      </Button>
      <Button
        variant="editor"
        size="editor"
        onClick={handleItalic}
        className={
          isItalicActive
            ? "rounded-none bg-neutral-800 text-neutral-50"
            : "rounded-none"
        }
      >
        <ItalicIcon />
      </Button>
      <Button
        variant="editor"
        size="editor"
        onClick={handleUnderline}
        className={
          isUnderlineActive
            ? "rounded-none bg-neutral-800 text-neutral-50"
            : "rounded-none"
        }
      >
        <u>U</u>
      </Button>
      <Button
        variant="editor"
        size="editor"
        onClick={handleStrike}
        className={
          isStrikeActive
            ? "rounded-l-none rounded-r-none bg-neutral-800 text-neutral-50"
            : "rounded-l-none rounded-r-none"
        }
      >
        <s>S</s>
      </Button>
      <Button
        variant="editor"
        size="editor"
        onClick={handleClearMarks}
        className={"rounded-l-none rounded-r-sm"}
      >
        <RemoveFormatting />
      </Button>
    </div>
  );
};

// Memoized font size controls
const FontSizeControls = React.memo(() => {
  return (
    <div className="divide-input flex divide-x rounded-sm">
      <TextIncreaseDecrease
        by={-0.5}
        label="Decrease font size"
        className={"rounded-l-sm rounded-r-none"}
      >
        {"<"}
      </TextIncreaseDecrease>
      <TextSize />
      <TextIncreaseDecrease
        by={0.5}
        label="Increase font size"
        className={"rounded-l-none rounded-r-sm"}
      >
        {">"}
      </TextIncreaseDecrease>
    </div>
  );
});

FontSizeControls.displayName = "FontSizeControls";

const TextIncreaseDecrease = React.memo(
  ({
    children,
    by,
    label,
    className,
  }: {
    children: React.ReactNode;
    by: number;
    label: string;
    className?: string;
  }) => {
    const dispatch = useDispatch();
    const abilityTextSize = useGetStoreState("abilityTextSize");

    const handleClick = useCallback(() => {
      dispatch(onAbilityTextSize(by));
    }, [dispatch, by]);

    const isDisabled = useMemo(
      () => abilityTextSize === 1 && label === "Decrease font size",
      [abilityTextSize, label],
    );

    return (
      <Button
        variant="editor"
        size="editor"
        onClick={handleClick}
        aria-label={label}
        title={label}
        className={cn("px-2", className)}
        disabled={isDisabled}
      >
        {children}
      </Button>
    );
  },
);

TextIncreaseDecrease.displayName = "TextIncreaseDecrease";

const TextSize = React.memo(() => {
  const size = useSelector(
    (state: storeState) => state.mainFormSlice.abilityTextSize,
  );
  const dispatch = useDispatch();

  const handleClick = useCallback(() => {
    dispatch(onAbilityTextSize("default"));
  }, [dispatch]);

  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>
        <Button
          variant="editor"
          size="editor"
          onClick={handleClick}
          aria-label="Reset font size"
          className="min-w-[4rem] rounded-l-none rounded-r-none"
        >
          <div className="flex content-center justify-center text-center">
            <p>{size + "px"}</p>
          </div>
        </Button>
      </TooltipTrigger>
      <TooltipContent>Reset font size</TooltipContent>
    </Tooltip>
  );
});

TextSize.displayName = "TextSize";
export default function AbilityInput({
  backgroundToggle = false,
}: {
  backgroundToggle?: boolean;
}) {
  const editorState = useGetStoreState("editorState");
  const dispatch = useDispatch();

  const [openedHelp, setOpenedHelp] = useState(false);
  const [openedAbilities, setOpenedAbilities] = useState(false);

  const openHelp = () => setOpenedHelp(true);
  const openAbilities = () => setOpenedAbilities(true);

  // const notStageRoute = route.Characters !== "Stage";
  // const notEventRoute = route.Characters !== "Event";
  // const notDonRoute = route.Characters !== "Don";
  const editor = useEditor({
    content: editorState,
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        // Disable the default Bold and Italic since we're using custom ones
        bold: false,
        italic: false,
      }),
      Underline,
      // Superscript,
      // SubScript,
      Highlight,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Color,
      CustomParagraph,
      CustomBold,
      CustomItalic,
    ],
    editorProps: {
      attributes: {
        class: "ability-input",
      },
    },
    onUpdate({ editor }) {
      dispatch(onAbility(editor.getHTML()));
    },
    onDestroy() {
      dispatch(onEditorState(editor?.getJSON()));
    },
    onBlur() {
      dispatch(onEditorState(editor?.getJSON()));
    },
    onCreate({ editor }) {
      editor.commands.setContent(editorState);
    },
  });
  return (
    <>
      <div className="flex flex-col">
        <label className="text-sm">Ability</label>
        <p className="text-xs text-[#868e96]">Card ability</p>
        <div className="bg-background relative mt-1 max-w-full rounded-md border lg:max-w-[697.42px] dark:bg-neutral-800">
          {/* Toolbar */}
          <div className="flex flex-row flex-wrap gap-2 gap-y-2 rounded-t-md border-b p-2 dark:bg-neutral-900/70">
            <div className="flex gap-1">
              <FormattingToolbar editor={editor} />
            </div>

            <div className={"flex"}>
              <ColorPickerButton editor={editor} />
            </div>

            {editor && (
              <BubbleMenu
                editor={editor}
                tippyOptions={{
                  offset: [80, -90],
                  zIndex: 10,
                  hideOnClick: false,
                  interactive: true,
                  placement: 'top',
                  sticky: true,
                  moveTransition: 'transform 0.1s ease-out'
                }}
                className="bg-popover divide-input flex items-center divide-x rounded-md border shadow-md"
              >
                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#2F77B3").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-l-md rounded-r-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#2F77B3]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    className="border-0 bg-transparent p-0"
                    arrowPadding={0}
                  >
                    <p className={"blue-ability-tooltip z-10 text-[16px]!"}>
                      OnPlay
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#d94880").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#d94880]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                    <p className={"pink-ability-tooltip z-10 text-[16px]!"}>
                      Once Per Turn
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#DC8535").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#DC8535]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                    <span
                      className={
                        "orange-ability-container top-[0.2rem] z-10 text-[20px]!"
                      }
                    >
                      <span className={"orange-ability-shadow"}>
                        <span className={"orange-ability"}>Blocker</span>
                      </span>
                    </span>
                  </TooltipContent>
                </Tooltip>

                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#ba212f").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#ba212f]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                    <p className={"counter-ability-tooltip z-10 text-[16px]!"}>
                      Counter
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#f8ed70").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#f8ed70]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                    <p className={"trigger-ability-tooltip z-10 text-[10px]!"}>
                      Trigger
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#FFFFFF").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#FFFFFF]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="number-ability-tooltip z-10 border-0 bg-transparent p-0 shadow-none">
                    <p className={"number-ability-tooltip z-10 text-[16px]!"}>
                      1
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip disableHoverableContent>
                  <TooltipTrigger asChild>
                    <Button
                      variant="editor"
                      size="editor"
                      onClick={() =>
                        editor.chain().focus().setColor("#000000").run()
                      }
                      className="bg-popover h-9 w-9.5 rounded-none px-2! py-2"
                    >
                      <div
                        className={"h-full w-full rounded-full bg-[#000000]"}
                      ></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="don-ability-tooltip z-10 border-0 bg-transparent p-0 shadow-none">
                    <p className={"don-ability-tooltip z-10 text-[16px]!"}>
                      DON!!×1
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Button
                  variant="editor"
                  size="editor"
                  onClick={() => editor.chain().focus().toggleItalic().run()}
                  className={`bg-popover h-9 w-9.5 rounded-none px-2! py-2 text-lg! ${
                    editor.isActive("customItalic")
                      ? "bg-neutral-800 text-neutral-50"
                      : "bg-transparent"
                  }`}
                >
                  <ItalicIcon />
                </Button>

                <Button
                  variant="editor"
                  size="editor"
                  onClick={() => editor.chain().focus().unsetColor().run()}
                  className={
                    "bg-popover h-9 w-9.5 rounded-l-none rounded-r-md px-2! py-2"
                  }
                >
                  <CircleSlash2 className={"w-[1.417rem]! rotate-90"} />
                </Button>
              </BubbleMenu>
            )}

            <div className="divide-input border-input flex divide-x rounded-sm border">
              <FontSizeControls />
            </div>

            <DropShadowButton />
            <AbilityDropShadowButton />
            {backgroundToggle && <Background />}

            <Button
              variant="outline"
              size="sm"
              onClick={openAbilities}
              title="Ability List"
              className={"h-[34px] px-2! text-[14px]!"}
            >
              Ability List
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={openHelp}
              title="Help"
              className={"h-[34px] px-2! text-[14px]!"}
            >
              Help
            </Button>
          </div>

          {/* Editor Content */}
          <EditorContent
            editor={editor}
            className="font-roboto min-h-20 overflow-clip"
          />

          <DefaultStateSetter editor={editor} />
        </div>

        <Dialog open={openedHelp} onOpenChange={setOpenedHelp}>
          <DialogContent
            className="flex flex-col gap-0 sm:max-w-lg"
            disableCloseIcon
          >
            <DialogTitle className="sr-only">Help</DialogTitle>
            <p>
              Just select the text to style it, you can style both Trigger and
              Ability text.
            </p>
            <br />
            <p>
              A bubble menu that includes all ability styles represented by
              their respective colors will appear, you can also remove the style
              by selecting the last option in the menu.
            </p>
            <br />
            <p>
              For trigger text, press &quot;Enter&quot; to create a new line in
              case you want the text to start below{" "}
              <span className="trigger-text">
                <span className="trigger-ability-container">
                  <span className="trigger-ability-shadow">
                    <span className="trigger-ability mt-1">Trigger</span>{" "}
                  </span>
                </span>
              </span>
            </p>
            <br />
            <p>
              You can use the <strong>&quot;Background&quot;</strong> button to
              toggle the background of the ability text.
            </p>
            <br />
            <p>
              You can use the <strong>&quot;Text Outline&quot;</strong> and{" "}
              <strong>&quot;Ability Outline&quot;</strong> buttons to toggle the
              white outline of the text and the ability text respectively.
            </p>
            <br />
            <p>
              You can change the font size by pressing <strong>{`"<"`}</strong>{" "}
              or <strong>{`">"`}</strong>, and you can reset it by pressing the
              current font size button.
            </p>
            <br />
            <p>
              Pro Tip: you can use the &quot;×&quot; sign instead of
              &quot;x&quot; to make the DON!! ability more accurate e.g.{" "}
              <span className="black-ability-container">
                <span className="black-ability-shadow">
                  <span className="black-ability">DON!!×1</span>{" "}
                </span>
              </span>
            </p>
          </DialogContent>
        </Dialog>

        <Dialog open={openedAbilities} onOpenChange={setOpenedAbilities}>
          <DialogContent className="flex flex-col gap-1 sm:max-w-lg">
            <DialogTitle className="sr-only">Ability List</DialogTitle>
            <p>
              <span className="blue-ability">On Play</span>{" "}
              <span className="blue-ability">On Block</span>{" "}
              <span className="blue-ability">On K.O.</span>{" "}
              <span className="blue-ability">Activate:Main</span>{" "}
              <span className="blue-ability">When Attacking</span>{" "}
              <span className="blue-ability">Your Turn</span>{" "}
              <span className="blue-ability">Opponent&apos;s turn</span>{" "}
              <span className="blue-ability">Main</span>{" "}
              <span className="blue-ability">When Attacked</span>{" "}
              <span className="blue-ability">Start of Your Turn</span>{" "}
              <span className="blue-ability">End of Your Turn</span>{" "}
              <span className="blue-ability">When Opponent Attacks</span>{" "}
            </p>
            <p>
              <span className="pink-ability">Once Per Turn</span>{" "}
            </p>
            <p>
              <span className="orange-ability-container">
                <span className="orange-ability-shadow">
                  <span className="orange-ability">Blocker</span>{" "}
                </span>
              </span>
              <span className="orange-ability-container">
                <span className="orange-ability-shadow">
                  <span className="orange-ability">Rush</span>{" "}
                </span>
              </span>
              <span className="orange-ability-container">
                <span className="orange-ability-shadow">
                  <span className="orange-ability">Double Attack</span>{" "}
                </span>
              </span>
              <span className="orange-ability-container">
                <span className="orange-ability-shadow">
                  <span className="orange-ability">Banish</span>{" "}
                </span>
              </span>
            </p>
            <p>
              <span className="red-ability">Counter</span>{" "}
            </p>
            <p className="trigger-text">
              <span className="trigger-ability-container">
                <span className="trigger-ability-shadow">
                  <span className="trigger-ability mt-1">Trigger</span>{" "}
                </span>
              </span>
            </p>
            <p>
              <span className="white-ability">1</span>{" "}
            </p>
            <p>
              <span className="black-ability-container">
                <span className="black-ability-shadow">
                  <span className="black-ability">DON!!×1</span>{" "}
                </span>
              </span>
            </p>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}

function DefaultStateSetter({ editor }: { editor: Editor | null }) {
  // Get the default state for the editor from some store
  const editorDefaultState = useGetStoreState("editorDefaultState");

  useEffect(() => {
    // Only set content if the editor exists
    if (editor && editorDefaultState) {
      editor.commands.setContent(editorDefaultState);
    }
  }, [editor, editorDefaultState]);

  // This component does not render anything
  return null;
}
